import React from 'react';
import styled from 'styled-components';

const OptionsContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin: 2rem 0;
  max-width: 800px;
  width: 100%;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 0.8rem;
  }
`;

const OptionButton = styled.button`
  padding: 1.5rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  background: white;
  color: #2d3748;
  font-size: 1.1rem;
  font-weight: 500;
  text-align: left;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: 80px;
  display: flex;
  align-items: center;
  position: relative;
  
  &:hover {
    border-color: #667eea;
    background: #f7fafc;
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
  
  /* 正确答案样式 */
  &.correct {
    border-color: #48bb78;
    background: #f0fff4;
    color: #22543d;
  }
  
  /* 错误答案样式 */
  &.incorrect {
    border-color: #f56565;
    background: #fff5f5;
    color: #742a2a;
  }
  
  /* 选中但未确认的样式 */
  &.selected {
    border-color: #667eea;
    background: #edf2f7;
    color: #2d3748;
  }
  
  @media (max-width: 480px) {
    padding: 1.2rem;
    font-size: 1rem;
    min-height: 70px;
  }
`;

const OptionLabel = styled.span`
  position: absolute;
  top: 0.5rem;
  left: 0.8rem;
  font-size: 0.8rem;
  font-weight: 600;
  color: #a0aec0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const OptionText = styled.span`
  margin-top: 0.5rem;
  line-height: 1.4;
`;

const OptionsArea = ({ 
  options = [], 
  selectedOption = null, 
  correctOption = null, 
  showResult = false, 
  onOptionSelect,
  disabled = false 
}) => {
  const getOptionClass = (index) => {
    if (!showResult) {
      return selectedOption === index ? 'selected' : '';
    }
    
    if (index === correctOption) {
      return 'correct';
    }
    
    if (selectedOption === index && index !== correctOption) {
      return 'incorrect';
    }
    
    return '';
  };

  const handleOptionClick = (index) => {
    if (disabled || showResult) return;
    onOptionSelect?.(index);
  };

  return (
    <OptionsContainer>
      {options.map((option, index) => (
        <OptionButton
          key={index}
          className={getOptionClass(index)}
          onClick={() => handleOptionClick(index)}
          disabled={disabled}
        >
          <OptionLabel>{String.fromCharCode(65 + index)}</OptionLabel>
          <OptionText>{option}</OptionText>
        </OptionButton>
      ))}
    </OptionsContainer>
  );
};

export default OptionsArea;
