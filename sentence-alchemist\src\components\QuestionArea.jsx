import React from 'react';
import styled from 'styled-components';

const QuestionContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  margin: 2rem 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  min-height: 200px;
`;

const QuestionText = styled.h2`
  color: white;
  font-size: 1.8rem;
  font-weight: 600;
  text-align: center;
  line-height: 1.4;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  max-width: 800px;
  
  @media (max-width: 768px) {
    font-size: 1.4rem;
  }
  
  @media (max-width: 480px) {
    font-size: 1.2rem;
  }
`;

const QuestionLabel = styled.div`
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 1px;
`;

const QuestionArea = ({ sentence, isLoading = false }) => {
  return (
    <QuestionContainer>
      <QuestionLabel>English Sentence</QuestionLabel>
      <QuestionText>
        {isLoading ? 'Loading...' : sentence || 'No sentence available'}
      </QuestionText>
    </QuestionContainer>
  );
};

export default QuestionArea;
