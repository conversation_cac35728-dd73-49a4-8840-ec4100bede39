import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import QuestionArea from '../components/QuestionArea';
import OptionsArea from '../components/OptionsArea';
import FeedbackArea from '../components/FeedbackArea';
import { gameController, GAME_STATE, GAME_MODE } from '../utils/gameController';

const PageContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const Header = styled.header`
  text-align: center;
  margin-bottom: 2rem;
`;

const Title = styled.h1`
  color: #2d3748;
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  @media (max-width: 768px) {
    font-size: 2rem;
  }
`;

const Subtitle = styled.p`
  color: #718096;
  font-size: 1.1rem;
  margin: 0;
`;

const GameContainer = styled.div`
  max-width: 900px;
  width: 100%;
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 2rem;
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  margin-bottom: 2rem;
  overflow: hidden;
`;

const ProgressFill = styled.div`
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 4px;
  transition: width 0.3s ease;
  width: ${props => props.progress}%;
`;

const StatsContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem;
  background: #f7fafc;
  border-radius: 12px;
  
  @media (max-width: 480px) {
    flex-direction: column;
    gap: 0.5rem;
  }
`;

const StatItem = styled.div`
  text-align: center;
`;

const StatLabel = styled.div`
  font-size: 0.8rem;
  color: #718096;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const StatValue = styled.div`
  font-size: 1.5rem;
  color: #2d3748;
  font-weight: 700;
`;

const TrainingPage = () => {
  const [gameState, setGameState] = useState(gameController.getGameState());
  const [selectedOption, setSelectedOption] = useState(null);

  // 更新游戏状态
  const updateGameState = () => {
    const newState = gameController.getGameState();
    setGameState(newState);
  };

  // 处理选项选择
  const handleOptionSelect = (index) => {
    if (gameState.state !== GAME_STATE.QUESTION) return;

    setSelectedOption(index);
    gameController.submitAnswer(index);

    // 立即更新状态
    updateGameState();

    // 确保状态更新被捕获，添加一个小延迟
    setTimeout(() => {
      updateGameState();
    }, 50);
  };

  // 进入下一题
  const handleNext = () => {
    if (gameState.state === GAME_STATE.FEEDBACK) {
      gameController.loadNextQuestion();
      setSelectedOption(null);
      updateGameState();
    }
  };

  // 组件挂载时开始游戏
  useEffect(() => {
    gameController.startGame(GAME_MODE.PRACTICE, {
      maxQuestionsPerSession: 20,
      adaptiveDifficulty: true
    });
    updateGameState();

    // 监听游戏状态变化 - 更频繁的更新以确保及时反馈
    const interval = setInterval(updateGameState, 50);

    return () => {
      clearInterval(interval);
      gameController.resetGame();
    };
  }, []);



  // 计算统计数据
  const stats = gameState.sessionStats;
  const accuracy = stats.questionsAnswered > 0 ? Math.round((stats.correctAnswers / stats.questionsAnswered) * 100) : 0;
  const progress = (stats.questionsAnswered / 20) * 100; // 假设最大20题

  return (
    <PageContainer>
      <Header>
        <Title>句之炼金</Title>
        <Subtitle>通过句子学习，掌握真正的语言能力</Subtitle>
      </Header>

      <GameContainer>
        <StatsContainer>
          <StatItem>
            <StatLabel>题目数</StatLabel>
            <StatValue>{stats.questionsAnswered}</StatValue>
          </StatItem>
          <StatItem>
            <StatLabel>正确率</StatLabel>
            <StatValue>{accuracy}%</StatValue>
          </StatItem>
          <StatItem>
            <StatLabel>连续正确</StatLabel>
            <StatValue>{stats.streak}</StatValue>
          </StatItem>
          <StatItem>
            <StatLabel>总分</StatLabel>
            <StatValue>{Math.round(stats.totalScore)}</StatValue>
          </StatItem>
        </StatsContainer>

        <ProgressBar>
          <ProgressFill progress={progress} />
        </ProgressBar>

        {gameState.state === GAME_STATE.LOADING && (
          <div style={{ textAlign: 'center', padding: '2rem' }}>
            <p>正在加载题目...</p>
          </div>
        )}

        {gameState.currentQuestion && gameState.state !== GAME_STATE.LOADING && (
          <>
            <QuestionArea
              sentence={gameState.currentQuestion.english}
              isLoading={gameState.state === GAME_STATE.LOADING}
            />

            <OptionsArea
              options={gameState.currentOptions || []}
              selectedOption={selectedOption}
              correctOption={gameState.correctIndex}
              showResult={gameState.state === GAME_STATE.FEEDBACK}
              onOptionSelect={handleOptionSelect}
              disabled={gameState.state !== GAME_STATE.QUESTION}
            />



            {gameState.state === GAME_STATE.FEEDBACK && gameState.currentResult && (
              <FeedbackArea
                isCorrect={gameState.currentResult.isCorrect}
                correctAnswer={gameState.currentResult.isCorrect ? null : gameState.currentResult.correctAnswer}
                feedback={gameState.currentResult.feedback}
                score={gameState.currentResult.score}
                onNext={handleNext}
                autoNext={true}
                autoNextDelay={3000}
              />
            )}

          </>
        )}

        {gameState.state === GAME_STATE.COMPLETED && (
          <div style={{ textAlign: 'center', padding: '2rem' }}>
            <h2>游戏完成！</h2>
            <p>总题数: {stats.questionsAnswered}</p>
            <p>正确率: {accuracy}%</p>
            <p>总分: {Math.round(stats.totalScore)}</p>
            <p>最高连击: {stats.maxStreak}</p>
            <button
              onClick={() => {
                gameController.startGame(GAME_MODE.PRACTICE);
                setSelectedOption(null);
                updateGameState();
              }}
              style={{
                padding: '1rem 2rem',
                fontSize: '1.1rem',
                backgroundColor: '#667eea',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                marginTop: '1rem'
              }}
            >
              再来一局
            </button>
          </div>
        )}
      </GameContainer>
    </PageContainer>
  );
};

export default TrainingPage;
