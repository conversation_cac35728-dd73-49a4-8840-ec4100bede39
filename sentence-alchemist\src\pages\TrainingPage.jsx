import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import QuestionArea from '../components/QuestionArea';
import OptionsArea from '../components/OptionsArea';
import FeedbackArea from '../components/FeedbackArea';
import { getRandomSentence, generateOptions } from '../data/sampleSentences';

const PageContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const Header = styled.header`
  text-align: center;
  margin-bottom: 2rem;
`;

const Title = styled.h1`
  color: #2d3748;
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  @media (max-width: 768px) {
    font-size: 2rem;
  }
`;

const Subtitle = styled.p`
  color: #718096;
  font-size: 1.1rem;
  margin: 0;
`;

const GameContainer = styled.div`
  max-width: 900px;
  width: 100%;
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 2rem;
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  margin-bottom: 2rem;
  overflow: hidden;
`;

const ProgressFill = styled.div`
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 4px;
  transition: width 0.3s ease;
  width: ${props => props.progress}%;
`;

const StatsContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem;
  background: #f7fafc;
  border-radius: 12px;
  
  @media (max-width: 480px) {
    flex-direction: column;
    gap: 0.5rem;
  }
`;

const StatItem = styled.div`
  text-align: center;
`;

const StatLabel = styled.div`
  font-size: 0.8rem;
  color: #718096;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const StatValue = styled.div`
  font-size: 1.5rem;
  color: #2d3748;
  font-weight: 700;
`;

const TrainingPage = () => {
  const [currentSentence, setCurrentSentence] = useState(null);
  const [options, setOptions] = useState([]);
  const [correctIndex, setCorrectIndex] = useState(null);
  const [selectedOption, setSelectedOption] = useState(null);
  const [showResult, setShowResult] = useState(false);
  const [isCorrect, setIsCorrect] = useState(null);
  const [stats, setStats] = useState({
    total: 0,
    correct: 0,
    streak: 0
  });
  const [usedSentenceIds, setUsedSentenceIds] = useState([]);

  // 初始化新题目
  const initializeQuestion = () => {
    const sentence = getRandomSentence(usedSentenceIds);
    const { options: generatedOptions, correctIndex: correctIdx } = generateOptions(sentence);
    
    setCurrentSentence(sentence);
    setOptions(generatedOptions);
    setCorrectIndex(correctIdx);
    setSelectedOption(null);
    setShowResult(false);
    setIsCorrect(null);
    
    // 记录已使用的句子ID
    setUsedSentenceIds(prev => {
      const newIds = [...prev, sentence.id];
      // 如果所有句子都用过了，重置列表
      if (newIds.length >= 5) { // 假设我们有5个示例句子
        return [sentence.id];
      }
      return newIds;
    });
  };

  // 处理选项选择
  const handleOptionSelect = (index) => {
    if (showResult) return;
    
    setSelectedOption(index);
    const correct = index === correctIndex;
    setIsCorrect(correct);
    setShowResult(true);
    
    // 更新统计数据
    setStats(prev => ({
      total: prev.total + 1,
      correct: prev.correct + (correct ? 1 : 0),
      streak: correct ? prev.streak + 1 : 0
    }));
  };

  // 进入下一题
  const handleNext = () => {
    initializeQuestion();
  };

  // 组件挂载时初始化第一题
  useEffect(() => {
    initializeQuestion();
  }, []);

  const accuracy = stats.total > 0 ? Math.round((stats.correct / stats.total) * 100) : 0;

  return (
    <PageContainer>
      <Header>
        <Title>句之炼金</Title>
        <Subtitle>通过句子学习，掌握真正的语言能力</Subtitle>
      </Header>

      <GameContainer>
        <StatsContainer>
          <StatItem>
            <StatLabel>题目数</StatLabel>
            <StatValue>{stats.total}</StatValue>
          </StatItem>
          <StatItem>
            <StatLabel>正确率</StatLabel>
            <StatValue>{accuracy}%</StatValue>
          </StatItem>
          <StatItem>
            <StatLabel>连续正确</StatLabel>
            <StatValue>{stats.streak}</StatValue>
          </StatItem>
        </StatsContainer>

        <ProgressBar>
          <ProgressFill progress={accuracy} />
        </ProgressBar>

        {currentSentence && (
          <>
            <QuestionArea 
              sentence={currentSentence.english}
              isLoading={false}
            />
            
            <OptionsArea
              options={options}
              selectedOption={selectedOption}
              correctOption={correctIndex}
              showResult={showResult}
              onOptionSelect={handleOptionSelect}
              disabled={showResult}
            />
            
            {showResult && (
              <FeedbackArea
                isCorrect={isCorrect}
                correctAnswer={isCorrect ? null : currentSentence.chinese}
                onNext={handleNext}
                autoNext={true}
                autoNextDelay={3000}
              />
            )}
          </>
        )}
      </GameContainer>
    </PageContainer>
  );
};

export default TrainingPage;
