// 示例句子数据
export const sampleSentences = [
  {
    id: 1,
    english: "Can I have a window seat, please?",
    chinese: "可以给我一个靠窗的座位吗？",
    distractors: [
      "我能看见一个窗户座位吗？",
      "我有一个靠窗的座位。",
      "请坐在窗户旁边。"
    ],
    scene: "airport",
    difficulty: "beginner"
  },
  {
    id: 2,
    english: "How much does this cost?",
    chinese: "这个多少钱？",
    distractors: [
      "这个花费了多少？",
      "这个成本是多少？",
      "多少钱能买这个？"
    ],
    scene: "shopping",
    difficulty: "beginner"
  },
  {
    id: 3,
    english: "Could you recommend a good restaurant nearby?",
    chinese: "你能推荐一家附近的好餐厅吗？",
    distractors: [
      "你能建议一个好的餐厅在附近吗？",
      "你可以推荐附近的餐厅吗？",
      "能告诉我附近哪里有好餐厅吗？"
    ],
    scene: "travel",
    difficulty: "intermediate"
  },
  {
    id: 4,
    english: "I'd like to check in, please.",
    chinese: "我想办理入住手续。",
    distractors: [
      "我想要检查一下。",
      "我想要登记入住。",
      "我要查看房间。"
    ],
    scene: "hotel",
    difficulty: "beginner"
  },
  {
    id: 5,
    english: "What time does the meeting start?",
    chinese: "会议什么时候开始？",
    distractors: [
      "会议开始的时间是什么？",
      "什么时候会议开始？",
      "会议几点钟开始？"
    ],
    scene: "work",
    difficulty: "beginner"
  }
];

// 获取随机句子的函数
export const getRandomSentence = (excludeIds = []) => {
  const availableSentences = sampleSentences.filter(
    sentence => !excludeIds.includes(sentence.id)
  );
  
  if (availableSentences.length === 0) {
    return sampleSentences[Math.floor(Math.random() * sampleSentences.length)];
  }
  
  return availableSentences[Math.floor(Math.random() * availableSentences.length)];
};

// 生成选项数组的函数
export const generateOptions = (sentence) => {
  const options = [sentence.chinese, ...sentence.distractors];
  
  // 随机打乱选项顺序
  for (let i = options.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [options[i], options[j]] = [options[j], options[i]];
  }
  
  // 返回选项数组和正确答案的索引
  const correctIndex = options.indexOf(sentence.chinese);
  
  return {
    options,
    correctIndex
  };
};
